#!/usr/bin/env python3
"""
处理AbdomenAtlas数据集的标签文件，提取肾脏标签（标签2和3）
将其重新映射为新标签文件中的标签1和2
使用多进程加速处理
"""

import os
import glob
import nibabel as nib
import numpy as np
from multiprocessing import Pool, cpu_count
from pathlib import Path
import argparse
from tqdm import tqdm

def process_single_file(args):
    """处理单个标签文件"""
    image_file, label_file, output_dir = args
    
    try:
        # 获取文件名（不含扩展名）
        filename = os.path.basename(image_file)
        
        # 检查对应的标签文件是否存在
        if not os.path.exists(label_file):
            print(f"Warning: Label file not found for {filename}")
            return False
        
        # 读取标签文件
        label_nii = nib.load(label_file)
        label_data = label_nii.get_fdata()
        
        # 创建新的标签数组，初始化为0
        new_label_data = np.zeros_like(label_data, dtype=np.uint8)
        
        # 提取肾脏标签：原标签2->新标签1，原标签3->新标签2
        new_label_data[label_data == 2] = 1  # 左肾
        new_label_data[label_data == 3] = 2  # 右肾
        
        # 检查是否有肾脏标签
        if np.sum(new_label_data) == 0:
            print(f"Warning: No kidney labels found in {filename}")
        
        # 创建新的NIfTI图像
        new_label_nii = nib.Nifti1Image(new_label_data, label_nii.affine, label_nii.header)
        
        # 保存到输出目录
        output_path = os.path.join(output_dir, filename)
        nib.save(new_label_nii, output_path)
        
        return True
        
    except Exception as e:
        print(f"Error processing {filename}: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Process AbdomenAtlas kidney labels')
    parser.add_argument('--image_dir', type=str, 
                       default='/home/<USER>/DiffTumor/data/HealthyCT/CT/05_AbdomenAtlas',
                       help='Directory containing image files')
    parser.add_argument('--label_dir', type=str,
                       default='/home/<USER>/DiffTumor/data/Dataset100_AbdomenAtlasMini/labelsTr',
                       help='Directory containing label files')
    parser.add_argument('--output_dir', type=str,
                       default='/home/<USER>/DiffTumor/data/HealthyCT/label/abdomenatlas_kidney_label',
                       help='Output directory for processed labels')
    parser.add_argument('--num_processes', type=int, default=8,
                       help='Number of processes to use')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取所有图像文件
    image_files = glob.glob(os.path.join(args.image_dir, "*.nii.gz"))
    image_files.sort()
    
    print(f"Found {len(image_files)} image files")
    
    # 准备处理参数
    process_args = []
    for image_file in image_files:
        filename = os.path.basename(image_file)
        label_file = os.path.join(args.label_dir, filename)
        process_args.append((image_file, label_file, args.output_dir))
    
    # 使用多进程处理
    print(f"Processing with {args.num_processes} processes...")
    
    with Pool(processes=args.num_processes) as pool:
        results = list(tqdm(
            pool.imap(process_single_file, process_args),
            total=len(process_args),
            desc="Processing files"
        ))
    
    # 统计结果
    successful = sum(results)
    failed = len(results) - successful
    
    print(f"\nProcessing completed:")
    print(f"  Successful: {successful}")
    print(f"  Failed: {failed}")
    print(f"  Output directory: {args.output_dir}")

if __name__ == "__main__":
    main()
